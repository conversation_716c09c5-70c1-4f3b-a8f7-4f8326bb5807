/* VenueAddForm.css */

/* Main Container */
.venue-form-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 10px 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 960px;
  margin: 10px auto;
  height: 97vh;
  overflow-y: scroll;
  scrollbar-width: none;
}

/* Back Button */
.venue-form-back-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 25px;
}

.venue-form-back-btn:hover {
  background-color: #2980b9;
}

/* Title */
.venue-form-title {
  font-size: 2.2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Message */
.venue-form-message {
  padding: 12px 18px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 500;
}

/* Success Message */
.venue-form-message {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

/* Error Message */
.venue-form-message.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

/* Form */
.venue-form {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Responsive columns */
gap: 25px; /* Increased gap */
padding: 25px;
background-color: #fff;
border-radius: 12px;
box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.venue-form-row{
display: flex;
  flex-wrap: wrap;  /* Allow wrapping to the next line */
  gap: 20px;        /* Space between items in a row */
  align-items: flex-start;
}

.venue-form-group {
  flex: 1 1 250px; /* Flexible, with a minimum width */
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.venue-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
  font-size: 1rem;
   display: flex;         /* Use flexbox to align icon and text */
align-items: center;   /* Vertically center */
gap: 10px;
}

.venue-form-input,
.venue-form-select,
.venue-form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.venue-form-textarea {
  min-height: 120px;
  resize: vertical;
}

.venue-form-input:focus,
.venue-form-select:focus,
.venue-form-textarea:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* Checkbox Group */
.venue-form-checkbox-group {
  grid-column: span 2; /* Span across all columns */
   display: flex;
  flex-direction: column;
}

/* Search Container within Checkbox Group */
.venue-search-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 15px;
  transition: box-shadow 0.3s ease;
}

.venue-search-container:focus-within {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.venue-search-icon {
  color: #3498db;
  margin-right: 10px;
  font-size: 1.1rem;
}

.venue-search-input {
  flex-grow: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #495057;
  background-color: transparent;
}

/* Checkbox Container */
.venue-form-checkbox-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Responsive columns */
  gap: 10px;
  max-height: 200px; /* Limit height and add scroll */
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.venue-form-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.venue-form-checkbox-label input{
  width: 1rem;
}

.venue-form-checkbox-label:hover{
background-color: #f0f3ff;
}
.venue-access-checkbox {
  margin-right: 8px;
  cursor: pointer;
}

/* Submit Button */
.venue-form-submit-btn {
background-color: #2ecc71;
color: white;
border: none;
padding: 14px 28px;
border-radius: 8px;
cursor: pointer;
font-size: 1.1rem;
transition: all 0.3s ease;
box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
align-self: center;
display: inline-flex;
align-items: center;
gap: 10px;
margin-top: 10px; /* Space from form */
grid-column: 1 / -1;                  /*  Full width */
}

.venue-form-submit-btn:hover {
  background-color: #27ae60;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .venue-form {
      grid-template-columns: 1fr; /* Single column on smaller screens */
  }
  .venue-form-row{
  flex-direction: column;
}
  .venue-form-checkbox-container {
      grid-template-columns: 1fr; /* Single column for checkboxes on small screens */
  }
}