/* ---------- Global Styling ---------- */
.supervisor-form-container {
  max-width: 600px;
  margin: 40px auto;
  padding: 25px;
  background: linear-gradient(135deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  font-family: "Poppins", sans-serif;
  animation: fadeInSlide 0.8s ease-in-out;
  position: relative;
  overflow: hidden;
}

/* Form Entrance Animation */
@keyframes fadeInSlide {
  from {
      opacity: 0;
      transform: translateY(-30px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

/* Title Animation */
.supervisor-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  animation: slideInTitle 0.8s ease-in-out;
}

@keyframes slideInTitle {
  from {
      opacity: 0;
      transform: translateY(-20px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

/* Floating Background Animation */
.supervisor-form-container::before {
  content: "";
  position: absolute;
  top: -50px;
  left: -50px;
  width: 120px;
  height: 120px;
  background: rgba(0, 123, 255, 0.2);
  border-radius: 50%;
  z-index: 0;
  animation: floating 5s infinite alternate ease-in-out;
}

.supervisor-form-container::after {
  content: "";
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  background: rgba(255, 165, 0, 0.2);
  border-radius: 50%;
  z-index: 0;
  animation: floating 6s infinite alternate ease-in-out;
}

@keyframes floating {
  from {
      transform: translateY(-10px) translateX(-10px);
  }
  to {
      transform: translateY(10px) translateX(10px);
  }
}

/* Form Styling */
.supervisor-form {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  gap: 0.8rem;
}

/* Label Styling */
label {
  font-weight: 500;
  color: #555;
  margin-bottom: 6px;
  display: block;
  font-size: 0.95rem;
  transition: 0.3s ease-in-out;
}

/* Floating Label Effect */
input:focus + label,
select:focus + label {
  transform: translateY(-20px);
  font-size: 0.85rem;
  color: #007bff;
}

/* Input Fields */
input,
select {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease-in-out;
  background: #fff;
  position: relative;
}

/* Glowing Effect on Focus */
input:focus,
select:focus {
  border-color: #007bff;
  box-shadow: 0 0 12px rgba(0, 123, 255, 0.4);
  outline: none;
  transform: scale(1.03);
}

/* Animated Submit Button */
button {
  margin-top: 15px;
  padding: 12px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
  position: relative;
}

/* Button Hover - 3D Bounce */
button:hover {
  background: linear-gradient(135deg, #0056b3, #003580);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 123, 255, 0.5);
}

/* Button Click Effect */
button:active {
  transform: scale(0.98);
}

/* Success/Error Message */
.supervisor-message {
  text-align: center;
  font-weight: bold;
  font-size: 1rem;
  padding: 10px;
  border-radius: 6px;
  margin-top: 10px;
  opacity: 0;
  animation: fadeInMessage 0.5s ease-in-out forwards;
}

@keyframes fadeInMessage {
  from {
      opacity: 0;
      transform: translateY(-10px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

.supervisor-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.supervisor-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .supervisor-form-container {
      width: 90%;
      padding: 20px;
  }

  .supervisor-title {
      font-size: 1.5rem;
  }

  button {
      font-size: 0.95rem;
  }
}
