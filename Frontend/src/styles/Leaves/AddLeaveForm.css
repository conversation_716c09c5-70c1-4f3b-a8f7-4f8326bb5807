/* AddLeaveForm.css */

/* Main Container */
.leaves-form-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 900px;
  margin: 30px auto;
  max-height: 100vh;
  overflow-y: scroll;
  scrollbar-width: none;
}

/* Back Button */
.leaves-back-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 25px;
}

.leaves-back-btn:hover {
  background-color: #2980b9;
}

/* Title */
.leaves-title {
  font-size: 2.2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Subtitle */
.leaves-subtitle {
  font-size: 1.5rem;
  color: #34495e;
  margin-top: 25px;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

/* Message */
.leaves-message {
padding: 12px 18px;
border-radius: 8px;
text-align: center;
margin-bottom: 25px;
font-weight: 500;
}
.leaves-message {
color: #155724;
background-color: #d4edda;
border: 1px solid #c3e6cb;
}

/* Error Message */
.leaves-message.error {
color: #721c24;
background-color: #f8d7da;
border: 1px solid #f5c6cb;
}

/* Search Bar */
.leaves-search-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
  transition: box-shadow 0.3s ease;
}

.leaves-search-container:focus-within {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.leaves-search-icon {
  color: #3498db;
  margin-right: 10px;
  font-size: 1.1rem;
}

.leaves-search-input {
  flex-grow: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #495057;
  background-color: transparent;
}

/* Student Select */
.leaves-student-select-section{
text-align: center;
}
.leaves-select {
  width: 100%;
  max-width: 400px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f8f8f8;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 20px;
  appearance: none; /* Remove default dropdown arrow */
}

.leaves-select:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* Existing Leaves List */
.leaves-list-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Responsive columns */
  gap: 20px;
  margin-bottom: 30px;
  max-height: 400px; /*  Set a maximum height  */
  overflow-y: auto;  /*  Enable vertical scrolling */
  padding-right: 10px; /* Add some padding to the right */
}

/* Optional: Hide the scrollbar visually (but keep functionality) */
.leaves-list-container::-webkit-scrollbar {
  width: 0px; /*  Hide scrollbar in WebKit browsers (Chrome, Safari) */
}

.leaves-list-container {
  scrollbar-width: none;  /* Hide scrollbar in Firefox */
  -ms-overflow-style: none;  /* Hide scrollbar in IE and Edge */
}

.leaves-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.leaves-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.leaves-card div{
display: flex;
align-items: center;
gap: 10px;
font-weight: 500;
margin-bottom: 8px;
}

.leaves-card-actions {
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
  gap: 10px;
  margin-top: 15px;
}

.leaves-edit-btn,
.leaves-delete-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}
.leaves-delete-btn{
background-color: #e74c3c;
}

.leaves-edit-btn:hover,
.leaves-delete-btn:hover {
  background-color: #2980b9;
}
.leaves-delete-btn:hover{
background-color: #c0392b;
}

/* Form */
.leaves-form {
  display: grid;
  grid-template-columns: 1fr; /* Start with single column */
  gap: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
}
.leaves-form-row{
 display: flex;
  flex-wrap: wrap;  /* Allow wrapping to the next line */
  gap: 20px;        /* Space between items in a row */
  align-items: flex-start;
}
.leaves-form-group {
  flex: 1 1 300px;  /* Flexible, but with a minimum width */
  min-width: 0;   /* Add this */
  display: flex;        /* Add this */
  flex-direction: column; /* Add this */
}

.leaves-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
  font-size: 1rem;
}

.leaves-input,
.leaves-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.leaves-textarea {
  min-height: 120px;
  resize: vertical;
}

.leaves-input:focus,
.leaves-textarea:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* Form Actions */
.leaves-form-actions {
  display: flex;
  justify-content: center; /* Center buttons */
  gap: 15px;
  margin-top: 20px;
}

.leaves-submit-btn,
.leaves-cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  display: inline-flex; /* Align icon and text */
  align-items: center;
  gap: 8px;
}

.leaves-submit-btn {
  background-color: #2ecc71; /* Green */
  color: white;
}
.leaves-cancel-btn{
background-color: #3498db; /* Green */
  color: white;
}
.leaves-submit-btn:hover {
  background-color: #27ae60;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.leaves-cancel-btn:hover{
background-color: #2980b9;
box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
/* Responsive Design */
@media (max-width: 768px) {
  .leaves-form {
      grid-template-columns: 1fr; /* Single column on smaller screens */
  }
  .leaves-form-row{
  flex-direction: column;
}
}