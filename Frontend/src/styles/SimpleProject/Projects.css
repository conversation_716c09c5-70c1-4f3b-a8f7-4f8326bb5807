
/* General Styles */
.projects-container {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f0f4ff; 
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  max-width: 1200px;
  margin: 30px auto;
}

/* Header */
.projects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #dee2e6;
}

.projects-title {
  font-size: 2.2rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
}

.projects-edit-button {
  background: linear-gradient(to right, #6a7ee7, #8b34eb);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.projects-edit-button:hover {
  background: linear-gradient(to right, #5a6ece, #782dc8);
}

/* Search Bar */
.projects-search {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 12px 18px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 25px;
  transition: box-shadow 0.3s ease;
}
.projects-search:focus-within {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.projects-search-icon {
  color: #7784e4;
  margin-right: 12px;
  font-size: 1.1rem;
}

.projects-search-input {
  border: none;
  outline: none;
  font-size: 1rem;
  color: #495057;
  flex-grow: 1;
  background-color: transparent;
}


/* --- Projects Grid --- */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 0.5fr)); 
  gap: 25px; 
}
/* Project Card */
.project-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  padding: 25px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  /* justify-content: space-between;  Removed this */
  /* Remove any height constraint */
   height: auto;  /* Let the height be determined by content */
  min-height: 0;  /* Reset any minimum height */
}

.project-card:hover {
transform: translateY(-5px);
box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.project-card-header {
margin-bottom: 15px;
border-bottom: 1px solid #e9f0ff; /* Light blue border */
padding-bottom: 12px;
}

.project-card-title {
font-size: 1.6rem;
color: #3c49a8; /* Darker violet title */
margin: 0;
}

.project-card-supervisor {
  font-size: 1rem;
  color: #777;
  margin-top: 5px;
}

.project-card-body {
  /* flex-grow: 1;  Removed this */
  margin-bottom: 15px;  /* Add some spacing before the actions */
}

/* Meta Information */
.project-card-meta {
font-size: 0.95rem;
color: #666;
line-height: 1.6;
}

.project-card-meta p {
  margin: 8px 0;
  display: flex;
  align-items: center;
}

.project-card-meta svg {
margin-right: 8px;
color: #7784e4; /* Light violet icon */
}

/* Project Details */
.project-card-details {
margin-top: 15px;
font-size: 0.9rem;
color: #555;
}

.project-card-details p {
  margin: 8px 0;
  display: flex;
  align-items: center;
}

.project-card-details svg {
  margin-right: 8px;
  color: #7784e4; /* Light violet icon */
}

.project-card-details a {
color: #5a69e4; /* Blue link */
text-decoration: none;
transition: color 0.3s ease;
}

.project-card-details a:hover {
  text-decoration: underline;
  color: #4552b8; /* Darker blue link */
}

/* Actions */
.project-card-actions {
  margin-top: 20px; /* Added margin */
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}
.project-card-toggle-button,
.project-card-notes-button {
  background: linear-gradient(to right, #5a69e4, #7784e4); /* Blue gradient */
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}
.project-card-toggle-button:hover,
.project-card-notes-button:hover {
background: linear-gradient(to right, #4a57b8, #636fbb); /* Darker blue gradient */
}

/* No Data State */
.no-data {
text-align: center;
padding: 20px;
font-style: italic;
color: #999;
}

/* Error Message */
.error-message {
color: #a94442;
text-align: center;
padding: 15px;
background: #f2dede;
border: 1px solid #ebccd1;
border-radius: 6px;
}

/* Divider */
.project-divider {
border-color: #d8e2f9; /* Light blue divider */
margin: 20px 0;
}

/* Notes Modal Overlay */
.project-notes-overlay {
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
background-color: rgba(0, 0, 0, 0.5);
display: flex;
justify-content: center;
align-items: center;
z-index: 1000;
}

.project-notes-content {
background: #fff;
padding: 25px;
border-radius: 12px;
width: 85%;
max-width: 900px;
position: relative;
box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  max-height: 80vh;  /* Limit height */
  overflow-y: auto;  /* Enable scrolling */
}

.project-close-notes-button {
position: absolute;
top: 10px;
right: 10px;
background: none;
border: none;
font-size: 20px;
cursor: pointer;
color: #555;
}
.project-close-notes-button:hover {
  color: #333;
}


