/* loginPage.css */

/* Main Container */
.loginpage-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 40px; /* Increased padding */
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 450px; /* Reduced max-width for better focus */
  margin: 50px auto; /* Increased margin */
}

/* Title */
.loginpage-title {
  font-size: 2.5rem; /* Increased font size */
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 700; /* Bolder font */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

/* Form */
.loginpage-form {
  display: flex;
  flex-direction: column;
  gap: 20px; /* Increased gap */
}

/* Input Group */
.loginpage-input-group {
  display: flex;        /* Use flexbox for alignment */
  flex-direction: column;
}

.loginpage-input-group label {
display: flex;
align-items: center;
gap: 10px;
margin-bottom: 5px; /* Space between label and input */
color: #555;
font-weight: 500;
}

.loginpage-input {
  width: 100%;
  padding: 14px; /* Increased padding */
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f9f9f9; /* Slightly lighter background */
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box; /* Include padding in width */
}

.loginpage-input:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* Error Message */
.loginpage-error {
  color: #e74c3c;
  background-color: #fde8e8;
  border: 1px solid #f5c6cb;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 20px;
}

/* Login Button */
.loginpage-button {
  background-color: #2ecc71; /* Green */
  color: white;
  border: none;
  padding: 14px 28px; /* Increased padding */
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: background-color 0.3s ease, transform 0.2s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16); /* More pronounced shadow */
   display: inline-flex;  /* Align icon with text */
align-items: center;
gap: 10px;
justify-content: center;
}

.loginpage-button:hover {
  background-color: #27ae60;
  transform: translateY(-2px); /* Slight lift */
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.24);
}