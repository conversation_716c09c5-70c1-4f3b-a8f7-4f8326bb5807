/* General Navbar Styling */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #1B1A55;
  padding: 12px 20px;
  color: white;
  width: 100vw;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  border-bottom: 1px solid rgb(155, 155, 155);
}


/* Logo and Branding */
.navbar-brand {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* Logo Hover Animation */
.navbar-logo {
  height: 50px;
  width: auto;
  border-radius: 5px;
  transition: transform 0.3s ease-in-out;
  /* mix-blend-mode:multiply */
}

.navbar-logo:hover {
  transform: scale(1.1) rotate(8deg);
}

/* Navigation Menu */
.navbar-menu {
  display: flex;
  align-items: center;
}

/* User Greeting */
.navbar-user {
  margin-right: 15px;
  font-size: 16px;
  font-weight: 500;
  animation: fadeIn 0.5s ease-in-out forwards;
}

/* Navigation Links */
.navbar-links {
  list-style: none;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
}

.navbar-links li {
  margin-left: 15px;
  animation: fadeIn 0.6s ease-in-out forwards;
  position: relative;
}

/* Glow Effect on Hover */
.navbar-links a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  font-size: 15px;
  transition: color 0.3s ease-in-out, transform 0.2s ease-in-out;
  position: relative;
  padding-bottom: 5px;
}

.navbar-links a:hover {
  color: #f39c12;
  transform: translateY(-2px);
}

/* Border Animation under links */
.navbar-links a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  background-color: #f39c12;
  left: 50%;
  bottom: 0;
  transition: width 0.3s ease-in-out, left 0.3s ease-in-out;
}

.navbar-links a:hover::after {
  width: 100%;
  left: 0;
}

/* Logout Button */
.navbar-logout {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 8px 14px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 5px;
  transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
  animation: pulse 1.5s infinite;
  margin: 0 auto;
}

/* Logout Button Hover Effect */
.navbar-logout:hover {
  background-color: #c0392b;
  transform: scale(1.05);
}

/* Pulsating Effect */
@keyframes pulse {
  0% {
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(231, 76, 60, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
  }
}

/* Mobile Menu Toggle */
.menu-toggle {
  display: none;
  font-size: 24px;
  cursor: pointer;
  color: white;
  transition: transform 0.3s ease-in-out;
}

.menu-toggle:hover {
  transform: scale(1.2);
}



/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
