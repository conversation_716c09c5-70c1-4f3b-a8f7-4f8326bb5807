/* FinanceBudgetList.css */

/* Main Container */
.financebudgetlist-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.financebudgetlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #dee2e6;
}

/* Title */
.financebudgetlist-title {
  font-size: 2rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
  display: flex; /* Use flexbox to align icon and text */
  align-items: center; /* Center vertically */
  gap: 10px; /* Spacing between icon and text */
}

.financebudgetlist-title-icon {
  color: #3498db; /* Example color - adjust as needed */
  margin-right: 8px; /* Space between icon and title text */
  font-size: 1.8rem;
}

/* Add/Edit <PERSON> */
.financebudgetlist-add-edit-btn {
background-color: #2ecc71;
color: white;
padding: 10px 18px;
border: none;
border-radius: 8px;
cursor: pointer;
font-size: 1rem;
transition: background-color 0.3s ease;
box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
display: inline-flex; /* For aligning icon with text */
align-items: center; /* Center items vertically */
gap: 8px; /* Space between icon and text */
}

.financebudgetlist-add-edit-btn:hover {
  background-color: #27ae60;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Loading, Error, and No Data Messages */
.financebudgetlist-loading,
.financebudgetlist-error,
.financebudgetlist-no-data {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: #777;
}

.financebudgetlist-error {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
}

/* Table Wrapper (for responsive scrolling) */
.financebudgetlist-table-wrapper {
  overflow-x: auto;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  /* Remove explicit height constraint */
}

/* Table Styles */
.financebudgetlist-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  table-layout: auto;  /*  Let the browser figure out column widths */
}

.financebudgetlist-table th,
.financebudgetlist-table td {
  padding: 15px;  /*  Consistent padding */
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  white-space: nowrap; /* Prevent text wrapping */
}
.financebudgetlist-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #34495e;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;  /*  Make headers sticky */
  top: 0;           /*  Stick to the top */
  z-index: 2;        /*  Ensure they're above the content */
  /* Add a subtle shadow to the sticky header */
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.1);
}
/* Zebra striping and hover effect */
.financebudgetlist-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

.financebudgetlist-table tbody tr:hover {
  background-color: #f0f3ff;
}

/* Total Row */
.total-row {
  font-weight: bold;
  background-color: #e0e5ff; /* Light blue background */
}

/* Status Styles */
.status-approved {
  color: #28a745;
  font-weight: 500;
}

.status-pending {
  color: #ffc107;
  font-weight: 500;
}
.status-rejected {
  color: #dc3545;
  font-weight: bold;
}