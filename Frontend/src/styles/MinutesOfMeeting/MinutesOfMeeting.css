.mom-container {
    width: 100%;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.messages-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    scrollbar-width: none;
  }
  
  .message-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  
  .date-separator {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    color: #555;
    background: #e0e0e0;
    padding: 5px 10px;
    margin: 10px 0;
    border-radius: 10px;
    width: fit-content;
    align-self: center;
  }
  
  .message-item {
    display: flex;
    flex-direction: column;
    max-width: 70%;
    padding: 8px 12px;
    border-radius: 8px;
    word-wrap: break-word;
  }
  
  .sent {
    align-self: flex-end;
    background-color: #e0e5ff;
  }
  
  .received {
    align-self: flex-start;
    background-color: #98a7f5;
  }
  
  .message-time {
    display: flex;
    justify-content: end;
    font-size: 12px;
    color: #777;
    margin-top: 3px;
  }
  

  