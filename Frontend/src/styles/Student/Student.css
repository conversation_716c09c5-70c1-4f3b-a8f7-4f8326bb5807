/* Student.css - With Role Filter */

/* General Container Styles */
.student-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  max-width: 1100px;
  margin: 30px auto;
  overflow-x: auto;
}

/* Header Section */
.student-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #dee2e6;
}

.student-title {
  font-size: 2.4rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
}

.student-edit-button {
  background: linear-gradient(to right, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 12px 22px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.student-edit-button:hover {
  background: linear-gradient(to right, #0056b3, #003366);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Search Bar */
.student-search-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 12px 18px;
  border-radius: 10px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  transition: box-shadow 0.3s ease;
}

.student-search-container:focus-within {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.student-search-icon {
  color: #4a90e2;
  margin-right: 12px;
  font-size: 1.2rem;
}

.student-search-input {
  border: none;
  outline: none;
  font-size: 1.1rem;
  color: #333;
  flex-grow: 1;
  background-color: transparent;
  margin-right: 10px; /* Add margin-right */
}

/* Role Filter Select */
.student-role-select {
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  font-size: 1rem;
  color: #495057;
  background-color: #fff;
  cursor: pointer;
  appearance: none; /* Remove default dropdown arrow */
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  margin-right: 10px;
}

.student-role-select:focus {
  border-color: #80bdff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.student-filter-icon {
  color: #6c757d; /* Gray color for the filter icon */
  margin-left: auto; /* Push icon to the right */
  font-size: 1.2rem;
}

/* Table Container */
.student-table-container {
  overflow-x: auto;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

/* Role Sections */
.student-role-section {
  margin-bottom: 40px;
}

.student-role-title {
  font-size: 1.9rem;
  color: #3498db;
  margin-bottom: 20px;
  border-bottom: 3px solid #3498db;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

/* Table Styles */
.student-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

.student-table th,
.student-table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.student-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #444;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.student-table tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

.student-table tbody tr:hover {
  background-color: #f0f3ff;
}

/* Project List */
.student-project-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.student-project-item {
  margin-bottom: 10px;
  padding: 12px 18px;
  border-radius: 8px;
  background-color: #f0f8ff;
  border-left: 5px solid #3498db;
  display: flex;
  flex-direction: column;
  gap: 6px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.student-project-item:hover {
  transform: translateX(3px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
}

.student-project-item span {
  display: block;
}

.student-project-item span:first-child {
  font-weight: 500;
  color: #333;
}

/* No Data Message */
.student-no-data {
  text-align: center;
  font-style: italic;
  color: #777;
  padding: 30px;
}

/* Error Message */
.student-error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}