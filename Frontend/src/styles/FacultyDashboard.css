/* Faculty Dashboard Layout */
.faculty-dashboard-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f4f4f4, #e0e0e0);
  overflow: hidden;
  transition: all 0.4s ease-in-out;
}

/* Sidebar Styling */
.faculty-sidebar {
  width: 9rem;
  background-color: #1B1A55;
  color: white;
  padding: 20px;
  box-shadow: 4px 0 10px rgba(0, 0, 0, 0.3);
  transition: width 0.4s ease-in-out;
  position: relative;
  overflow: hidden;
}

/* Sidebar Collapsible */
.faculty-sidebar.collapsed {
  width: 80px;
  padding: 10px;
}

/* Sidebar Expand Effect */
.faculty-sidebar:hover {
  width: 15rem;
}

/* Sidebar List */
.faculty-sidebar-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.faculty-sidebar-list li {
  margin-bottom: 14px;
  position: relative;
  transition: transform 0.3s ease-in-out;
}

/* Sidebar Link Styling */
.faculty-sidebar-list a {
  text-decoration: none;
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  border-radius: 6px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: background 0.3s ease-in-out, transform 0.2s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

/* Sidebar Hover Effect */
.faculty-sidebar-list a:hover {
  background: linear-gradient(90deg, #f39c12, #e67e22);
  transform: translateX(6px);
  box-shadow: 2px 2px 10px rgba(243, 156, 18, 0.5);
}

/* Active Sidebar Link */
.faculty-sidebar-list li.active a {
  background: #e67e22;
  font-weight: bold;
  box-shadow: 0px 0px 15px rgba(230, 126, 34, 0.6);
}

/* Sidebar Icons */
.faculty-sidebar-list a i {
  font-size: 18px;
  transition: transform 0.3s ease-in-out;
}

/* Animate Icons on Hover */
.faculty-sidebar-list a:hover i {
  transform: rotate(10deg);
}

/* Ripple Effect on Click */
.faculty-sidebar-list a:active::after {
  content: "";
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  width: 100%;
  height: 100%;
  border-radius: inherit;
  transform: scale(0);
  animation: ripple 0.5s ease-out forwards;
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

/* Animated Active Indicator */
.faculty-sidebar-list li.active::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 60%;
  background: #f1c40f;
  border-radius: 5px;
  animation: glow 1s infinite alternate;
}

/* Glow Animation for Active Indicator */
@keyframes glow {
  from {
    box-shadow: 0px 0px 8px rgba(241, 196, 15, 0.5);
  }
  to {
    box-shadow: 0px 0px 16px rgba(241, 196, 15, 0.9);
  }
}

/* Main Content Area */
.faculty-main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: white;
  border-radius: 12px;
  margin: 20px;
  animation: slideIn 0.5s ease-in-out;
}

/* Slide-in Animation for Main Content */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Sidebar Toggle Button */
.faculty-sidebar-toggle {
  position: absolute;
  top: 20px;
  right: -30px;
  background: #e67e22;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 2px 2px 10px rgba(230, 126, 34, 0.5);
}

.faculty-sidebar-toggle:hover {
  background: #f39c12;
}

/* Responsive Sidebar Adjustments */
@media (max-width: 768px) {
  .faculty-sidebar {
    width: 220px;
  }

  .faculty-sidebar:hover {
    width: 240px;
  }

  .faculty-main-content {
    margin: 10px;
    padding: 15px;
  }
}
