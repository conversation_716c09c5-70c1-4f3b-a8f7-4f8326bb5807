/* UserEquipmentList.css - CORRECTED TABLE DISPLAY */

/* Main Container */
.equipment-list-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 1100px;
  margin: 30px auto;
}

/* Header Section */
.equipment-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #dee2e6;
}

.equipment-list-title {
  font-size: 2.2rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.equipment-list-title-icon {
  color: #3498db;
  font-size: 1.8rem;
}

/* Manage Button */
.manage-equipment-btn {
  background-color: #2ecc71; /* Green */
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.manage-equipment-btn:hover {
  background-color: #27ae60;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Loading and Error Messages */
.equipment-list-message {
  text-align: center;
  padding: 20px;
  font-style: italic;
  color: #777;
}

.loading {
  text-align: center;
}

.error {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

/* --- TABLE STYLES (CORRECTED) --- */
.equipment-table {
  width: 100%;
  border-collapse: collapse; /* Important for consistent borders */
  margin-top: 20px;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden; /* For rounded corners on the table */
  display: table; /*  CRUCIAL:  Restore table display */
}

.equipment-table th,
.equipment-table td {
  padding: 15px 20px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  display: table-cell; /* CRUCIAL: Restore table-cell display */
}

.equipment-table thead {
  display: table-header-group; /* CRUCIAL:  Restore table-header-group */
}

.equipment-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #444;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky; /* Keep sticky header */
  top: 0;
  z-index: 2;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.1); /* Shadow for sticky header */
}

.equipment-table tbody {
display: table-row-group;
}

.equipment-table tbody tr {
  display: table-row; /* CRUCIAL: Restore table-row display */
}

.equipment-table tbody tr:nth-child(even) {
  background-color: #f8f9fa; /* Zebra striping */
}

.equipment-table tbody tr:hover {
  background-color: #f0f3ff; /* Highlight on hover */
}

.equipment-table td:nth-child(3),  /* Location Column */
.equipment-table td:nth-child(4),  /* Added On Column */
.equipment-table td:nth-child(5) { /* Using User Column */
  display: table-cell; /* CRUCIAL: keep it as table-cell */
  align-items: center; /* Vertically center icon */
  gap: 8px;         /* Space between icon and text */
}