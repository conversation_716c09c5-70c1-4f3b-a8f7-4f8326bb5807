/* EquipmentAddForm.css */

/* Main Container */
.equipment-add-form-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 10px 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 960px;
  margin: 10px auto;
  height: 100vh;
  scrollbar-width: none;
  overflow-y: scroll;
}

/* Back Button */
.equipment-add-form-back-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 25px;
}

.equipment-add-form-back-btn:hover {
  background-color: #2980b9;
}

/* Title */
.equipment-add-form-title {
  font-size: 2.2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Message (Success/Error) */
.equipment-add-form-message {
  padding: 12px 18px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 500;
}

/* Success Message */
.equipment-add-form-message {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

/* Error Message */
.equipment-add-form-message.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

/* Search Bar */
.equipment-add-form-search-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
  transition: box-shadow 0.3s ease;
}

.equipment-add-form-search-container:focus-within {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.equipment-add-form-search-icon {
  color: #3498db;
  margin-right: 10px;
  font-size: 1.1rem;
}

.equipment-add-form-search-input {
  flex-grow: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #495057;
  background-color: transparent;
}

/* Existing Equipment List */
.equipment-add-form-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  max-height: 200px;  /*  Limit height, add scroll */
  overflow-y: auto;
}

.equipment-add-form-list-title {
  font-size: 1.4rem;
  color: #34495e;
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}
.equipment-add-form-items-list{
list-style: none;
padding: 0px;
}
.equipment-add-form-item {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.equipment-add-form-item:hover {
  background-color: #f0f8ff;
}

.equipment-add-form-item:last-child {
  border-bottom: none;
}

/* Form */
.equipment-add-form-form {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Make form responsive */
gap: 25px;
padding: 25px;
background-color: #fff;
border-radius: 12px;
box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

/* Form Row */
.equipment-form-row{
display: flex;
flex-wrap: wrap;
gap: 20px;
}

.equipment-add-form-group {
  flex: 1 1 300px;  /*  Flexible, with minimum width */
  min-width: 0;   /* Add this */
  display: flex;
  flex-direction: column;
}

.equipment-add-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
  font-size: 1rem;
}

.equipment-add-form-group input[type="text"],
.equipment-add-form-group input[type="date"],
.equipment-add-form-group input[type="number"],
.equipment-add-form-group select,
.equipment-add-form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.equipment-add-form-group textarea {
  min-height: 120px;
  resize: vertical;
}

.equipment-add-form-group input:focus,
.equipment-add-form-group select:focus,
.equipment-add-form-group textarea:focus {
  border-color: #888;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* Action Buttons */
.equipment-add-form-actions {
  grid-column: span 2; /* Make buttons span across all columns */
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}
.equipment-add-form-submit-btn,
.equipment-add-form-delete-btn, .equipment-add-form-cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
   display: inline-flex;
  align-items: center;
  gap: 8px;
}

.equipment-add-form-submit-btn {
  background-color: #2ecc71;
  color: white;
}

.equipment-add-form-delete-btn {
  background-color: #e74c3c;
  color: white;
}
.equipment-add-form-cancel-btn{
 background-color: #3498db;
  color: white;
}
.equipment-add-form-submit-btn:hover,
.equipment-add-form-delete-btn:hover, .equipment-add-form-cancel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .equipment-add-form-form {
      grid-template-columns: 1fr; /* Single column on smaller screens */
  }
  .equipment-form-row{
    flex-direction: column;
  }

  .equipment-add-form-actions {
      grid-column: 1; /* Reset grid column span */
  }
  .equipment-add-form-group{
    flex-basis: 100%;
  }
}