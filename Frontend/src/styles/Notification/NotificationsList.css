/* Enhanced NotificationsList.css */
.notification-container {
  padding: 20px;
  width: 70%;
  max-width: 900px;
  background: #ffffff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  border-left: 6px solid rgb(75, 92, 197);
  margin: 20px auto;
  transition: all 0.3s ease;
  max-height: 80vh;
  overflow-y: scroll;
  scrollbar-width: none;
}

.notification-container:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* Top Section */
.notification-top-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #ddd;
}

#notification-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

/* Add Notification Button */
.notification-add-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 20%;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.notification-add-btn:hover {
  background: #0056b3;
  transform: scale(1.1);
}

/* Error & Message Styles */
.notification-error,
.notification-message {
  text-align: center;
  font-size: 16px;
  color: #d9534f;
  font-weight: bold;
  margin-bottom: 10px;
}

/* Notification Table */
.notification-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.notification-table th,
.notification-table td {
  padding: 14px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

/* Table Header */
.notification-table th {
  background: #007bff;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}



.notification-table td{
  text-align: center;
  word-wrap: break-word;
}

/* Type Column - Emphasized */
.notification-table td:first-child {
  font-size: 28px;
  font-weight: bold;
}

/* Expired Notifications */
.notification-table .expired {
  background: rgba(64, 61, 61, 0.416);
  color: #dc3545;
  font-weight: bold;
}

/* Delete Button */
.notification-delete-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
}

/* Highlight on hover with translation effect */
.notification-table tr {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.notification-table tr:hover {
  border-radius: 30px;
  transform: translateX(10px);
}


/* Priority-based row colors */
.priority-high {
  background-color: rgba(240, 7, 27, 0.47); /* Light red */
}

.priority-medium {
  background-color: rgba(225, 234, 47, 0.2); /* Light yellow */
}

.priority-low {
  background-color: rgba(0, 255, 60, 0.44); /* Light green */
}





.notification-delete-btn:hover {
  color: #b02a37;
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-container {
    width: 95%;
  }
  .notification-table {
    font-size: 14px;
  }
  .notification-table th,
  .notification-table td {
    padding: 12px;
  }
  .notification-add-btn {
    font-size: 18px;
  }
}
