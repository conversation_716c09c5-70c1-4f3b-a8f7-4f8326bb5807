/* DisplaySponsors.css - FINAL, CORRECTED HOVER/WIDTH ISSUES */

/* General Container Styles */
.display-sponsors-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  max-width: 1100px;
  margin: 30px auto;
}

/* Header */
.display-sponsors-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #dee2e6;
}

.display-sponsors-title {
  font-size: 2.2rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
}

.display-sponsor-manage-btn {
  background-color: #3498db;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.display-sponsor-manage-btn:hover {
  background-color: #2980b9;
}

/* Search Bar */
.display-sponsors-search-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 12px 18px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 30px;
  transition: box-shadow 0.3s ease;
}

.display-sponsors-search-bar:focus-within {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.display-sponsors-search-bar .search-icon {
  color: #3498db;
  margin-right: 12px;
  font-size: 1.1rem;
}

.display-sponsors-search-bar input {
  border: none;
  outline: none;
  font-size: 1rem;
  color: #495057;
  flex-grow: 1;
  background-color: transparent;
}

/* Finance Budget Close Button */
.finance-budget-container {
  position: relative;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.close-budget-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 14px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.close-budget-btn:hover {
  background-color: #c0392b;
}

/* --- Sponsor List and Cards (CRITICAL FIXES HERE) --- */
.sponsor-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 25px;
}

.sponsor-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  padding: 25px;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s ease; /*  Only transition the box-shadow */
  /* Removed position: relative; */
}

/* Hover effect - ONLY change the box-shadow */
.sponsor-card:hover {
  /* Removed transform: translateY(-5px); */
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.sponsor-title {
  font-size: 1.5rem;
  color: #34495e;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.sponsor-details p {
  margin: 10px 0;
  font-size: 1rem;
  color: #555;
  display: flex;
  align-items: center;
}

.sponsor-details .detail-icon {
  margin-right: 10px;
  color: #3498db;
}

.sponsor-details a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.sponsor-details a:hover {
  text-decoration: underline;
  color: #2980b9;
}

.clickable-budget {
  color: #3498db;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s;
}

.clickable-budget:hover {
  text-decoration: underline;
  color: #2980b9;
}

/* --- Notes Button and Overlay --- */

/* Notes Button */
.sponsor-notes-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 15px;
  align-self: flex-end;
  font-size: 1rem;
}

.sponsor-notes-button:hover {
  background-color: #2980b9;
}

/* Overlay for Notes */
.sponsor-notes-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Content inside the overlay */
.sponsor-notes-content {
  background-color: white;
  padding: 30px;
  border-radius: 12px;
  width: 80%;
  max-width: 800px;
  height: 60%;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 80vh;
  overflow-y: auto;
}

/* Close button for the notes overlay */
.sponsor-close-notes-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #555;
  transition: color 0.3s ease;
  padding: 10px;
  background-color: grey;
  line-height: 1;
}

.sponsor-close-notes-button:hover {
  color: #333;
}

/* No Sponsors Message */
.no-sponsors-message {
  text-align: center;
  font-style: italic;
  color: #777;
  padding: 30px;
}