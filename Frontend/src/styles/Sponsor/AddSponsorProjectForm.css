/* AddSponsorProjectForm.css */

/* Main Container */
.add-sponsor-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 900px;
  margin: 30px auto;
  height: 97vh;
overflow: scroll;
}

/* Back Button */
.add-sponsor-back-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  margin-bottom: 20px;
  display: inline-flex; /* Align icon and text */
  align-items: center; /* Center vertically */
  gap: 8px; /* Space between icon and text */
}

.add-sponsor-back-btn:hover {
  background-color: #2980b9;
}

/* Title */
.add-sponsor-title {
  font-size: 2.2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
  text-transform: uppercase; /* Make title uppercase */
  letter-spacing: 0.5px;    /* Add letter spacing */
}

/* Message */
.add-sponsor-message {
  color: #28a745;  /* Success/green color */
  background-color: #e9f8ef; /* Light green background */
  border: 1px solid #bde7d3; /* Green border */
  padding: 14px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 500;
}

/* Error Message - different styling */
.add-sponsor-message.error {
color: #dc3545; /* Red color */
background-color: #f8d7da; /* Light red background */
border-color: #f5c6cb; /* Red border */
}


/* Search Bar */
.add-sponsor-search-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
  transition: box-shadow 0.3s ease;
}

.add-sponsor-search-container:focus-within {
box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.add-sponsor-search-icon {
  color: #3498db;
  margin-right: 10px;
  font-size: 1.1rem;
}

.add-sponsor-search-input {
  flex-grow: 1;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #495057;
  background-color: transparent; /* No background */
}

/* Existing Projects List */
.add-sponsor-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  max-height: 200px; /* Limiting height */
  overflow-y: auto;  /* Vertical scroll if needed */
}

.add-sponsor-list-title {
  font-size: 1.4rem;
  color: #34495e;
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px solid #3498db; /* Blue underline */
  padding-bottom: 10px;
}

.add-sponsor-project-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.add-sponsor-project-item {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-sponsor-project-item:hover {
  background-color: #f0f8ff; /* Light blue on hover */
}

.add-sponsor-project-item:last-child {
  border-bottom: none; /* No border on the last item */
}


/* Form */
.add-sponsor-form {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Make it responsive */
gap: 25px; /* Increased gap */
padding: 20px;
background-color: #fff;
border-radius: 10px;
box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

}

/* Form row */
.add-sponsor-form-row {
  display: flex;
  flex-wrap: wrap;  /* Allow wrapping */
  gap: 20px;        /* Space between items in a row */
  align-items: flex-start; /* Align items to top */
}

.add-sponsor-group {
  flex: 1 1 300px;  /* Flexible, but with a minimum width */
  min-width: 0;   /* Add this */
  display: flex;        /* Add this */
  flex-direction: column; /* Add this */
}

.add-sponsor-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
  font-size: 1rem;
}

.add-sponsor-group input[type="text"],
.add-sponsor-group input[type="url"],
.add-sponsor-group input[type="date"],
.add-sponsor-group input[type="number"],
.add-sponsor-group select,
.add-sponsor-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #444;
  background-color: #f9f9f9; /* Lighter background */
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box; /* Include padding in width */
}


.add-sponsor-group textarea {
  min-height: 120px; /* Increased height */
  resize: vertical;
}

.add-sponsor-group input:focus,
.add-sponsor-group select:focus,
.add-sponsor-group textarea:focus {
  border-color: #888; /* Darker border on focus */
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2); /* Focus ring */
}

/* Action Buttons */
.add-sponsor-actions {
  grid-column: span 2;  /* Make buttons span across all columns*/
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.add-sponsor-submit-btn,
.add-sponsor-delete-btn {
padding: 12px 24px;
border: none;
border-radius: 8px;
cursor: pointer;
font-size: 1.1rem;
transition: all 0.3s ease;
box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
display: inline-flex; /* Align icon with text */
align-items: center;
gap: 8px;
}

.add-sponsor-submit-btn {
  background-color: #2ecc71; /* Green */
  color: white;
}

.add-sponsor-delete-btn {
  background-color: #e74c3c; /* Red */
  color: white;
}

.add-sponsor-submit-btn:hover {
  background-color: #27ae60; /* Darker green */
   box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.add-sponsor-delete-btn:hover {
  background-color: #c0392b; /* Darker red */
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .add-sponsor-form {
      grid-template-columns: 1fr; /* Single column on smaller screens */
  }
   .add-sponsor-form-row{
  flex-direction: column;
}
  .add-sponsor-actions {
      grid-column: 1; /* Reset grid column span */
      flex-direction: column; /* Stack buttons vertically */
      align-items: stretch; /* Stretch buttons to full width */
  }

   .add-sponsor-group{
   flex-basis: 100%;
}
}