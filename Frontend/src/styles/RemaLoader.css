/* RemaLoader.css - Enhanced with Background Shapes */

/* --- Container --- */
.rema-loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, #74b9ff, #ffffff);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease-out;
}

.rema-loader-container.fade-out {
  opacity: 0;
  pointer-events: none;
}

/* --- Logo --- */
.rema-loader-logo {
  font-family: 'Pacifico', cursive;
  font-size: 8rem;
  font-weight: bold;
  margin-bottom: 40px;
  display: flex;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.rema-letter {
  display: inline-block;
  animation: wave 1.5s ease-in-out infinite;
  color: #3498db;
}

.rema-letter.r { animation-delay: 0s;   }
.rema-letter.e { animation-delay: 0.15s; }
.rema-letter.m { animation-delay: 0.3s; }
.rema-letter.a { animation-delay: 0.45s; }

@keyframes wave {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-25px); }
}

/* --- Spinner --- */
.rema-loader-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #3498db;
  margin-bottom: 20px;
}

/* --- Loading Text --- */
.rema-loader-text {
  font-size: 1.5rem;
  color: #2980b9;
  margin-top: 15px; /* Add some space above the text */
}

/* --- Animated Background Shapes --- */
.rema-loader-shapes {
  position: absolute; /*  Absolute positioning within the container */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden; /*  Hide shapes that extend beyond the container */
}

.rema-loader-shapes .shape {
  position: absolute;
  opacity: 0.2; /* Semi-transparent */
  animation: floatAndFade 8s ease-in-out infinite; /* Animation for movement and fading */
}

/* Circle */
.rema-loader-shapes .circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #3498db; /* Blue */
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

/* Square */
.rema-loader-shapes .square {
  width: 60px;
  height: 60px;
  background-color: #2ecc71; /* Green */
  top: 60%;
  left: 70%;
  animation-delay: 2s;
}

/* Triangle (using CSS borders) */
.rema-loader-shapes .triangle {
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 80px solid #e74c3c; /* Red */
  top: 10%;
  left: 80%;
  animation-delay: 4s;
}

/* Keyframes for Floating and Fading Animation */
@keyframes floatAndFade {
  0% {
      transform: translate(0, 0) scale(1);
      opacity: 0.2;
  }
  50% {
      transform: translate(20px, -30px) scale(1.3); /* Move and scale */
      opacity: 0.5; /* Increase opacity */
  }
  100% {
      transform: translate(40px, -10px) scale(1);
      opacity: 0.2;
  }
}