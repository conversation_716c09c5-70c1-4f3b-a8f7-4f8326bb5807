/* AddUser.css */

/* Main Container */
.add-user-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 30px auto;
}

/* Back Button */
.add-user-back-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 25px;
}

.add-user-back-btn:hover {
  background-color: #2980b9;
}

/* Title */
.add-user-title {
  font-size: 2.2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
   display: flex;         /* Use flexbox to align icon and text */
align-items: center;   /* Vertically center */
justify-content: center;
gap: 10px;
}

/* Message */
.add-user-message {
  padding: 12px 18px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 500;
}

.add-user-message {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

/* Error Message */
.add-user-message.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

/* Form */
.add-user-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 25px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}
.add-user-form-row{
display: flex;
  flex-wrap: wrap;  /* Allow wrapping to the next line */
  gap: 20px;        /* Space between items in a row */
  align-items: flex-start;
}
.add-user-form-group {
  flex: 1 1 300px;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.add-user-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
  font-size: 1rem;
    display: flex;         /* Use flexbox to align icon and text */
align-items: center;   /* Vertically center */
gap: 10px;
}

.add-user-form-group input[type="text"],
.add-user-form-group input[type="email"],
.add-user-form-group input[type="password"],
.add-user-form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.add-user-form-group input:focus,
.add-user-form-group select:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

/* Submit Button */
.add-user-submit-btn {
  background-color: #2ecc71;
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  align-self: center;  /* Center the button */
   display: inline-flex;
  align-items: center;
  gap: 8px;
}

.add-user-submit-btn:hover {
  background-color: #27ae60;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Switch Styles */
.switch {
position: relative;
display: inline-block;
width: 60px;
height: 34px;
}

.switch input {
opacity: 0;
width: 0;
height: 0;
}

.slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: #ccc;
-webkit-transition: .4s;
transition: .4s;
}

.slider:before {
position: absolute;
content: "";
height: 26px;
width: 26px;
left: 4px;
bottom: 4px;
background-color: white;
-webkit-transition: .4s;
transition: .4s;
}

input:checked + .slider {
background-color: #2196F3;
}

input:focus + .slider {
box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
-webkit-transform: translateX(26px);
-ms-transform: translateX(26px);
transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
border-radius: 34px;
}

.slider.round:before {
border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .add-user-form-row {
      flex-direction: column; /* Stack form elements */
  }

  .add-user-form-group{
    flex-basis: 100%;
  }
}